import React, { useState, useEffect } from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  Animated,
} from 'react-native';
import { speechRecognitionService } from '../services/SpeechRecognitionService';
import SpeechDebugPanel from './SpeechDebugPanel';

interface DebugFloatingButtonProps {
  enabled?: boolean;
}

const DebugFloatingButton: React.FC<DebugFloatingButtonProps> = ({
  enabled = __DEV__ // Only show in development by default
}) => {
  const [showPanel, setShowPanel] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [lastTranscription, setLastTranscription] = useState<string>('');
  const [position] = useState(new Animated.ValueXY({ x: 20, y: 100 }));

  useEffect(() => {
    if (!enabled) return;

    // Subscribe to speech recognition state
    const unsubscribe = speechRecognitionService.addListener((state) => {
      setIsListening(state.isListening);
      if (state.currentResult?.text) {
        setLastTranscription(state.currentResult.text);
      }
    });

    return unsubscribe;
  }, [enabled]);

  // Simplified positioning without gesture handling for now
  // Can be enhanced later with react-native-gesture-handler if needed

  if (!enabled) return null;

  return (
    <>
      <View
        style={[
          styles.floatingButton,
          {
            backgroundColor: isListening ? '#FF6B6B' : '#45B7D1',
          },
        ]}
      >
        <TouchableOpacity
          style={styles.button}
          onPress={() => setShowPanel(true)}
          activeOpacity={0.8}
        >
          <Text style={styles.buttonIcon}>
            {isListening ? '🎤' : '🔧'}
          </Text>
          <Text style={styles.buttonText}>DEBUG</Text>
          {lastTranscription && (
            <View style={styles.transcriptionPreview}>
              <Text style={styles.transcriptionText} numberOfLines={1}>
                {lastTranscription.slice(0, 10)}...
              </Text>
            </View>
          )}
        </TouchableOpacity>
      </View>

      <SpeechDebugPanel
        visible={showPanel}
        onClose={() => setShowPanel(false)}
      />
    </>
  );
};

const styles = StyleSheet.create({
  floatingButton: {
    position: 'absolute',
    top: 100,
    right: 20,
    width: 60,
    height: 80,
    borderRadius: 30,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    zIndex: 1000,
  },
  button: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
  },
  buttonIcon: {
    fontSize: 16,
    marginBottom: 2,
  },
  buttonText: {
    color: '#fff',
    fontSize: 8,
    fontWeight: '600',
    textAlign: 'center',
  },
  transcriptionPreview: {
    position: 'absolute',
    bottom: -20,
    left: -10,
    right: -10,
    backgroundColor: 'rgba(0,0,0,0.8)',
    borderRadius: 8,
    padding: 4,
  },
  transcriptionText: {
    color: '#fff',
    fontSize: 8,
    textAlign: 'center',
  },
});

export default DebugFloatingButton;
