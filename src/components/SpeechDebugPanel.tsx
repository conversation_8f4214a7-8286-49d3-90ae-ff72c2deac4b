import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Modal,
} from 'react-native';
import { speechRecognitionService } from '../services/SpeechRecognitionService';
import { DebugInfo, TranscriptionDebugData, SessionStats } from '../types';

interface SpeechDebugPanelProps {
  visible: boolean;
  onClose: () => void;
}

const SpeechDebugPanel: React.FC<SpeechDebugPanelProps> = ({ visible, onClose }) => {
  const [debugLogs, setDebugLogs] = useState<DebugInfo[]>([]);
  const [transcriptionHistory, setTranscriptionHistory] = useState<TranscriptionDebugData[]>([]);
  const [sessionStats, setSessionStats] = useState<SessionStats | null>(null);
  const [activeTab, setActiveTab] = useState<'logs' | 'transcriptions' | 'stats'>('logs');
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    if (!visible) return;

    // Subscribe to debug logs
    const unsubscribeDebug = speechRecognitionService.addDebugListener((debug: DebugInfo) => {
      setDebugLogs(prev => {
        const newLogs = [...prev, debug];
        // Keep only last 100 logs to prevent memory issues
        return newLogs.slice(-100);
      });
    });

    // Update transcription history and stats periodically
    const updateInterval = setInterval(() => {
      setTranscriptionHistory(speechRecognitionService.getTranscriptionHistory());
      setSessionStats(speechRecognitionService.getSessionStats());
    }, 1000);

    return () => {
      unsubscribeDebug();
      clearInterval(updateInterval);
    };
  }, [visible]);

  // Auto-scroll to bottom when new logs arrive
  useEffect(() => {
    if (activeTab === 'logs' && scrollViewRef.current) {
      scrollViewRef.current.scrollToEnd({ animated: true });
    }
  }, [debugLogs, activeTab]);

  const clearLogs = () => {
    setDebugLogs([]);
    speechRecognitionService.clearTranscriptionHistory();
    setTranscriptionHistory([]);
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      fractionalSecondDigits: 3,
    });
  };

  const getLogColor = (type: DebugInfo['type']) => {
    switch (type) {
      case 'error': return '#FF6B6B';
      case 'warning': return '#FFD93D';
      case 'transcription': return '#6BCF7F';
      case 'audio': return '#4ECDC4';
      case 'timing': return '#45B7D1';
      default: return '#95A5A6';
    }
  };

  const getLogIcon = (type: DebugInfo['type']) => {
    switch (type) {
      case 'error': return '❌';
      case 'warning': return '⚠️';
      case 'transcription': return '🎤';
      case 'audio': return '🔊';
      case 'timing': return '⏱️';
      default: return 'ℹ️';
    }
  };

  const renderLogs = () => (
    <ScrollView 
      ref={scrollViewRef}
      style={styles.scrollView}
      showsVerticalScrollIndicator={true}
    >
      {debugLogs.map((log, index) => (
        <View key={index} style={[styles.logItem, { borderLeftColor: getLogColor(log.type) }]}>
          <View style={styles.logHeader}>
            <Text style={styles.logIcon}>{getLogIcon(log.type)}</Text>
            <Text style={styles.logTimestamp}>{formatTimestamp(log.timestamp)}</Text>
            <Text style={[styles.logType, { color: getLogColor(log.type) }]}>
              {log.type.toUpperCase()}
            </Text>
          </View>
          <Text style={styles.logMessage}>{log.message}</Text>
          {log.data && (
            <Text style={styles.logData}>
              {typeof log.data === 'string' ? log.data : JSON.stringify(log.data, null, 2)}
            </Text>
          )}
        </View>
      ))}
    </ScrollView>
  );

  const renderTranscriptions = () => (
    <ScrollView style={styles.scrollView}>
      {transcriptionHistory.map((item, index) => (
        <View key={index} style={styles.transcriptionItem}>
          <View style={styles.transcriptionHeader}>
            <Text style={styles.transcriptionIndex}>#{item.sliceIndex || index + 1}</Text>
            <Text style={styles.transcriptionStatus}>
              {item.isCapturing ? '🔴 LIVE' : '✅ FINAL'}
            </Text>
            <Text style={styles.transcriptionConfidence}>
              {(item.confidence * 100).toFixed(1)}%
            </Text>
          </View>
          <Text style={styles.transcriptionText}>{item.text}</Text>
          <View style={styles.transcriptionTiming}>
            <Text style={styles.timingText}>
              Process: {item.processTime}ms | Record: {item.recordingTime}ms
            </Text>
            {item.segments && (
              <Text style={styles.segmentCount}>
                {item.segments.length} segments
              </Text>
            )}
          </View>
        </View>
      ))}
    </ScrollView>
  );

  const renderStats = () => (
    <ScrollView style={styles.scrollView}>
      {sessionStats && (
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>Session Duration</Text>
            <Text style={styles.statValue}>
              {(sessionStats.sessionDuration / 1000).toFixed(1)}s
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>Total Slices</Text>
            <Text style={styles.statValue}>{sessionStats.sliceCount}</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>Avg Processing Time</Text>
            <Text style={styles.statValue}>
              {sessionStats.averageProcessingTime.toFixed(1)}ms
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>Avg Confidence</Text>
            <Text style={styles.statValue}>
              {(sessionStats.averageConfidence * 100).toFixed(1)}%
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>Total Processing</Text>
            <Text style={styles.statValue}>
              {sessionStats.totalProcessingTime}ms
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>Total Recording</Text>
            <Text style={styles.statValue}>
              {sessionStats.totalRecordingTime}ms
            </Text>
          </View>
        </View>
      )}
    </ScrollView>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Speech Recognition Debug</Text>
          <View style={styles.headerButtons}>
            <TouchableOpacity onPress={clearLogs} style={styles.clearButton}>
              <Text style={styles.clearButtonText}>Clear</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Text style={styles.closeButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.tabContainer}>
          {(['logs', 'transcriptions', 'stats'] as const).map((tab) => (
            <TouchableOpacity
              key={tab}
              style={[styles.tab, activeTab === tab && styles.activeTab]}
              onPress={() => setActiveTab(tab)}
            >
              <Text style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.content}>
          {activeTab === 'logs' && renderLogs()}
          {activeTab === 'transcriptions' && renderTranscriptions()}
          {activeTab === 'stats' && renderStats()}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1a',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#2d2d2d',
    borderBottomWidth: 1,
    borderBottomColor: '#444',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  clearButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#FF6B6B',
    borderRadius: 4,
  },
  clearButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  closeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#45B7D1',
    borderRadius: 4,
  },
  closeButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#2d2d2d',
    borderBottomWidth: 1,
    borderBottomColor: '#444',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: '#45B7D1',
  },
  tabText: {
    color: '#ccc',
    fontSize: 14,
    fontWeight: '500',
  },
  activeTabText: {
    color: '#fff',
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    padding: 8,
  },
  logItem: {
    backgroundColor: '#2d2d2d',
    marginBottom: 8,
    padding: 12,
    borderRadius: 6,
    borderLeftWidth: 4,
  },
  logHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  logIcon: {
    fontSize: 12,
    marginRight: 8,
  },
  logTimestamp: {
    fontSize: 10,
    color: '#888',
    marginRight: 8,
  },
  logType: {
    fontSize: 10,
    fontWeight: '600',
  },
  logMessage: {
    color: '#fff',
    fontSize: 12,
    marginBottom: 4,
  },
  logData: {
    color: '#ccc',
    fontSize: 10,
    fontFamily: 'monospace',
    backgroundColor: '#1a1a1a',
    padding: 4,
    borderRadius: 2,
  },
  transcriptionItem: {
    backgroundColor: '#2d2d2d',
    marginBottom: 8,
    padding: 12,
    borderRadius: 6,
    borderLeftWidth: 4,
    borderLeftColor: '#6BCF7F',
  },
  transcriptionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  transcriptionIndex: {
    color: '#888',
    fontSize: 12,
    fontWeight: '600',
  },
  transcriptionStatus: {
    fontSize: 10,
    fontWeight: '600',
  },
  transcriptionConfidence: {
    color: '#6BCF7F',
    fontSize: 12,
    fontWeight: '600',
  },
  transcriptionText: {
    color: '#fff',
    fontSize: 14,
    marginBottom: 8,
    textAlign: 'right', // RTL for Arabic
  },
  transcriptionTiming: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timingText: {
    color: '#888',
    fontSize: 10,
  },
  segmentCount: {
    color: '#888',
    fontSize: 10,
  },
  statsContainer: {
    padding: 8,
  },
  statItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#2d2d2d',
    padding: 16,
    marginBottom: 8,
    borderRadius: 6,
  },
  statLabel: {
    color: '#ccc',
    fontSize: 14,
  },
  statValue: {
    color: '#45B7D1',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default SpeechDebugPanel;
