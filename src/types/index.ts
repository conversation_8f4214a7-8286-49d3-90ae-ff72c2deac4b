// Arabic Text Types
export interface ArabicText {
  id: string;
  title: string;
  content: string;
  source?: string;
  category?: string;
}

export interface TextSegment {
  id: string;
  text: string;
  startIndex: number;
  endIndex: number;
  isSelected: boolean;
  isRevealed: boolean;
}

// Speech Recognition Types
export interface SpeechRecognitionResult {
  text: string;
  confidence: number;
  segments?: Array<{
    text: string;
    start: number;
    end: number;
  }>;
}

export interface SpeechRecognitionOptions {
  language?: string;
  maxThreads?: number;
  useVad?: boolean;
  realtimeAudioSec?: number;
  realtimeAudioSliceSec?: number;
}

// Practice Session Types
export interface PracticeSession {
  id: string;
  textId: string;
  selectedSegments: TextSegment[];
  startTime: Date;
  endTime?: Date;
  progress: number;
  isCompleted: boolean;
  accuracy?: number;
}

export interface PracticeProgress {
  totalWords: number;
  revealedWords: number;
  currentWordIndex: number;
  accuracy: number;
  timeElapsed: number;
}

// Audio Types
export interface AudioPermissions {
  granted: boolean;
  canAskAgain: boolean;
}

export interface AudioSessionConfig {
  category: string;
  mode: string;
  options: string[];
}

// UI State Types
export interface AppState {
  isLoading: boolean;
  error?: string;
  currentText?: ArabicText;
  practiceSession?: PracticeSession;
  audioPermissions: AudioPermissions;
}

export interface TextDisplayState {
  selectedText: string;
  selectionStart: number;
  selectionEnd: number;
  isSelecting: boolean;
  hiddenSegments: TextSegment[];
}

export interface SpeechRecognitionState {
  isListening: boolean;
  isProcessing: boolean;
  currentResult?: SpeechRecognitionResult;
  error?: string;
}

// Enhanced debugging interfaces for development
export interface DebugInfo {
  timestamp: number;
  type: 'info' | 'warning' | 'error' | 'transcription' | 'audio' | 'timing';
  message: string;
  data?: any;
}

export interface TranscriptionDebugData {
  text: string;
  confidence: number;
  processTime: number;
  recordingTime: number;
  isCapturing: boolean;
  sliceIndex?: number;
  segments?: Array<{
    text: string;
    start: number;
    end: number;
    confidence?: number;
  }>;
}

export interface SessionStats {
  sessionDuration: number;
  totalProcessingTime: number;
  totalRecordingTime: number;
  sliceCount: number;
  averageProcessingTime: number;
  averageConfidence: number;
}

// Component Props Types
export interface ArabicTextDisplayProps {
  text: ArabicText;
  segments: TextSegment[];
  onSegmentSelect: (segment: TextSegment) => void;
  onSelectionChange: (start: number, end: number) => void;
}

export interface PracticeModeProps {
  session: PracticeSession;
  onWordRevealed: (wordIndex: number) => void;
  onSessionComplete: () => void;
  onSessionPause: () => void;
  onSessionReset: () => void;
}

export interface SpeechRecognitionProps {
  isActive: boolean;
  options: SpeechRecognitionOptions;
  onResult: (result: SpeechRecognitionResult) => void;
  onError: (error: string) => void;
  onStatusChange: (status: SpeechRecognitionState) => void;
}

// Configuration Types
export interface AppConfig {
  whisperModelPath: string;
  defaultLanguage: string;
  maxRecordingDuration: number;
  speechThreshold: number;
  autoSaveProgress: boolean;
}

// Error Types
export enum ErrorType {
  AUDIO_PERMISSION_DENIED = 'AUDIO_PERMISSION_DENIED',
  SPEECH_RECOGNITION_FAILED = 'SPEECH_RECOGNITION_FAILED',
  MODEL_LOADING_FAILED = 'MODEL_LOADING_FAILED',
  TEXT_PROCESSING_FAILED = 'TEXT_PROCESSING_FAILED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

export class AppError extends Error {
  public type: ErrorType;
  public details?: any;
  public timestamp: Date;

  constructor(options: {
    type: ErrorType;
    message: string;
    details?: any;
    timestamp: Date;
  }) {
    super(options.message);
    this.type = options.type;
    this.details = options.details;
    this.timestamp = options.timestamp;
    this.name = 'AppError';
  }
}

// Navigation Types
export type RootStackParamList = {
  Home: undefined;
  TextSelection: { textId: string };
  Practice: { sessionId: string };
  Settings: undefined;
  About: undefined;
};

// Storage Types
export interface StorageKeys {
  USER_PREFERENCES: 'user_preferences';
  PRACTICE_SESSIONS: 'practice_sessions';
  ARABIC_TEXTS: 'arabic_texts';
  APP_CONFIG: 'app_config';
}

export interface UserPreferences {
  preferredLanguage: string;
  fontSize: number;
  enableVibration: boolean;
  enableSoundFeedback: boolean;
  autoSaveProgress: boolean;
  speechRecognitionSensitivity: number;
}

// Utility Types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
