import { initWhisper, WhisperContext } from 'whisper.rn';
import { Platform } from 'react-native';
import {
  SpeechRecognitionResult,
  SpeechRecognitionOptions,
  SpeechRecognitionState,
  ErrorType,
  AppError
} from '../types';

export class SpeechRecognitionService {
  private whisperContext: WhisperContext | null = null;
  private isInitialized = false;
  private isInitializing = false;
  private initializationPromise: Promise<void> | null = null;
  private currentSubscription: (() => void) | null = null;
  private listeners: Array<(state: SpeechRecognitionState) => void> = [];
  private mockMode = false; // For development/testing

  constructor() {
    // Don't initialize immediately to avoid blocking the main thread
    // Initialize lazily when first needed

    // Disable mock mode to enable real speech recognition
    this.mockMode = false;

    console.log('SpeechRecognitionService: Running in production mode with real-time transcription');
  }

  private async initializeWhisper(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    if (this.isInitializing && this.initializationPromise) {
      return this.initializationPromise;
    }

    this.isInitializing = true;
    this.initializationPromise = this._performInitialization();

    try {
      await this.initializationPromise;
    } catch (error) {
      this.isInitializing = false;
      this.initializationPromise = null;
      throw error;
    }
  }

  private async _performInitialization(): Promise<void> {
    try {
      console.log('Initializing Whisper model for Arabic speech recognition...');
      console.log('Platform:', Platform.OS);
      console.log('Model: ggml-base-q8_0.bin (production model)');

      // Notify listeners that initialization is starting
      this.notifyListeners({
        isListening: false,
        isProcessing: true, // Use processing state to indicate loading
      });

      // Mock mode for development
      if (this.mockMode) {
        console.log('Mock mode: Simulating model initialization...');
        await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate loading time

        this.isInitialized = true;
        this.isInitializing = false;

        console.log('Mock mode: Whisper model initialized successfully');

        this.notifyListeners({
          isListening: false,
          isProcessing: false,
        });
        return;
      }

      // Use the production-ready ggml-base-q8_0.bin model for better Arabic recognition
      let initOptions: any = {
        filePath: require('../../assets/model/ggml-base-q8_0.bin'),
      };

      // Add platform-specific options
      if (Platform.OS === 'ios') {
        initOptions.useGpu = true;
      } else {
        // Android-specific optimizations
        initOptions.useGpu = false;
        initOptions.maxThreads = 2; // Limit threads on Android
      }

      console.log('Init options:', JSON.stringify(initOptions, null, 2));

      // Initialize Whisper with extended timeout for larger model
      // The ggml-base-q8_0.bin model is larger and may take longer to load
      const initPromise = initWhisper(initOptions);
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Initialization timeout after 60 seconds')), 60000);
      });

      console.log('Loading ggml-base-q8_0.bin model - this may take up to 60 seconds...');
      this.whisperContext = await Promise.race([initPromise, timeoutPromise]) as any;

      this.isInitialized = true;
      this.isInitializing = false;

      console.log('Whisper model initialized successfully');

      this.notifyListeners({
        isListening: false,
        isProcessing: false,
      });
    } catch (error) {
      this.isInitialized = false;
      this.isInitializing = false;

      console.error('Failed to initialize Whisper:', error);
      console.error('Error type:', typeof error);
      console.error('Error message:', (error as any)?.message);
      console.error('Error stack:', (error as any)?.stack);

      // Try to get more details about the error
      let errorMessage = 'Failed to initialize speech recognition model';
      const errorObj = error as any;
      if (errorObj?.message) {
        if (errorObj.message.includes('timeout')) {
          errorMessage = 'تم انتهاء وقت تحميل النموذج (60 ثانية). النموذج الكبير يحتاج وقت أطول. يرجى المحاولة مرة أخرى.';
        } else if (errorObj.message.includes('memory') || errorObj.message.includes('Memory')) {
          errorMessage = 'ذاكرة غير كافية لتحميل النموذج الكبير. يرجى إغلاق التطبيقات الأخرى والمحاولة مرة أخرى.';
        } else if (errorObj.message.includes('file') || errorObj.message.includes('File')) {
          errorMessage = 'لم يتم العثور على ملف النموذج ggml-base-q8_0.bin. يرجى إعادة تثبيت التطبيق.';
        }
      }

      this.notifyListeners({
        isListening: false,
        isProcessing: false,
        error: errorMessage,
      });

      throw new AppError({
        type: ErrorType.MODEL_LOADING_FAILED,
        message: errorMessage,
        details: error,
        timestamp: new Date(),
      });
    }
  }

  public async startListening(options: SpeechRecognitionOptions = {}): Promise<void> {
    // Ensure the service is initialized before starting
    await this.initializeWhisper();

    // Mock mode implementation
    if (this.mockMode) {
      console.log('Mock mode: Starting listening...');
      this.notifyListeners({
        isListening: true,
        isProcessing: false,
      });

      // Simulate speech recognition after a delay
      setTimeout(() => {
        this.notifyListeners({
          isListening: false,
          isProcessing: true,
          currentResult: {
            text: 'مرحبا بك في التطبيق', // Mock Arabic text
            confidence: 0.95,
          },
        });
      }, 3000);

      return;
    }

    if (!this.isInitialized || !this.whisperContext) {
      throw new AppError({
        type: ErrorType.SPEECH_RECOGNITION_FAILED,
        message: 'Speech recognition service not initialized',
        timestamp: new Date(),
      });
    }

    try {
      this.notifyListeners({
        isListening: true,
        isProcessing: false,
      });

      const transcribeOptions = {
        language: options.language || 'ar', // Default to Arabic
        maxThreads: options.maxThreads || (Platform.OS === 'android' ? 2 : 4),
        useVad: options.useVad !== false, // Enable VAD by default
        realtimeAudioSec: options.realtimeAudioSec || 30,
        realtimeAudioSliceSec: options.realtimeAudioSliceSec || 3, // Shorter slices for better responsiveness
        // Enhanced Arabic-specific optimizations for ggml-base-q8_0 model
        temperature: 0.0, // More deterministic results
        beamSize: 5, // Good balance between accuracy and speed
        bestOf: 5, // Multiple candidates for better accuracy
        wordThold: 0.01, // Lower threshold for Arabic words
        tokenTimestamps: true,
        // Additional optimizations for Arabic
        noSpeechThold: 0.6, // Adjust for Arabic speech patterns
        logprobThold: -1.0, // Better handling of Arabic phonemes
        compressionRatioThold: 2.4, // Optimized for Arabic text compression
      };

      const { stop, subscribe } = await this.whisperContext.transcribeRealtime(transcribeOptions);

      this.currentSubscription = stop;

      subscribe((event) => {
        const { isCapturing, data, processTime, recordingTime, error } = event;

        if (error) {
          console.error('Real-time transcription error:', error);
          this.notifyListeners({
            isListening: false,
            isProcessing: false,
            error: `خطأ في التعرف على الصوت: ${error}`,
          });
          return;
        }

        // Enhanced state management for real-time transcription
        const hasResult = data?.result && data.result.trim().length > 0;

        this.notifyListeners({
          isListening: isCapturing,
          isProcessing: !isCapturing && Boolean(data?.result),
          currentResult: hasResult ? {
            text: data.result.trim(),
            confidence: this.calculateConfidence(data),
            segments: data.segments?.map(segment => ({
              text: segment.text.trim(),
              start: segment.t0,
              end: segment.t1,
            })).filter(segment => segment.text.length > 0),
          } : undefined,
        });

        if (!isCapturing && hasResult) {
          // Final result received
          console.log(`Arabic speech recognition completed in ${processTime}ms`);
          console.log(`Recording time: ${recordingTime}ms`);
          console.log(`Transcribed text: "${data.result.trim()}"`);
        }
      });

    } catch (error) {
      console.error('Failed to start speech recognition:', error);
      this.notifyListeners({
        isListening: false,
        isProcessing: false,
        error: 'Failed to start speech recognition',
      });
      throw new AppError({
        type: ErrorType.SPEECH_RECOGNITION_FAILED,
        message: 'Failed to start speech recognition',
        details: error,
        timestamp: new Date(),
      });
    }
  }

  public async stopListening(): Promise<void> {
    // Mock mode implementation
    if (this.mockMode) {
      console.log('Mock mode: Stopping listening...');
      this.notifyListeners({
        isListening: false,
        isProcessing: false,
      });
      return;
    }

    if (this.currentSubscription) {
      try {
        this.currentSubscription();
        this.currentSubscription = null;
        this.notifyListeners({
          isListening: false,
          isProcessing: false,
        });
      } catch (error) {
        console.error('Failed to stop speech recognition:', error);
        this.notifyListeners({
          isListening: false,
          isProcessing: false,
          error: 'Failed to stop speech recognition',
        });
      }
    }
  }

  public async transcribeAudio(audioPath: string, options: SpeechRecognitionOptions = {}): Promise<SpeechRecognitionResult> {
    // Ensure the service is initialized before transcribing
    await this.initializeWhisper();

    if (!this.isInitialized || !this.whisperContext) {
      throw new AppError({
        type: ErrorType.SPEECH_RECOGNITION_FAILED,
        message: 'Speech recognition service not initialized',
        timestamp: new Date(),
      });
    }

    try {
      const transcribeOptions = {
        language: options.language || 'ar',
        maxThreads: options.maxThreads || (Platform.OS === 'android' ? 2 : 4),
        // Enhanced Arabic-specific optimizations for ggml-base-q8_0 model
        temperature: 0.0,
        beamSize: 5,
        bestOf: 5,
        wordThold: 0.01,
        tokenTimestamps: true,
        // Additional optimizations for Arabic
        noSpeechThold: 0.6,
        logprobThold: -1.0,
        compressionRatioThold: 2.4,
      };

      const { promise } = this.whisperContext.transcribe(audioPath, transcribeOptions);
      const result = await promise;

      return {
        text: result.result,
        confidence: this.calculateConfidence(result),
        segments: result.segments?.map(segment => ({
          text: segment.text,
          start: segment.t0,
          end: segment.t1,
        })),
      };
    } catch (error) {
      console.error('Failed to transcribe audio:', error);
      throw new AppError({
        type: ErrorType.SPEECH_RECOGNITION_FAILED,
        message: 'Failed to transcribe audio',
        details: error,
        timestamp: new Date(),
      });
    }
  }

  public addListener(listener: (state: SpeechRecognitionState) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  public async release(): Promise<void> {
    try {
      await this.stopListening();
      if (this.whisperContext) {
        await this.whisperContext.release();
        this.whisperContext = null;
      }
      this.isInitialized = false;
      this.listeners = [];
    } catch (error) {
      console.error('Failed to release speech recognition service:', error);
    }
  }

  private calculateConfidence(data: any): number {
    // Enhanced confidence calculation for Arabic speech recognition
    if (!data || !data.segments || data.segments.length === 0) {
      return 0.5; // Default confidence
    }

    // Calculate average confidence from segments
    const avgConfidence = data.segments.reduce((sum: number, segment: any) => {
      return sum + (segment.p || 0.5);
    }, 0) / data.segments.length;

    // Apply Arabic-specific confidence adjustments
    let adjustedConfidence = avgConfidence;

    // Boost confidence for longer Arabic text (more context)
    if (data.result && data.result.length > 20) {
      adjustedConfidence *= 1.1;
    }

    // Reduce confidence for very short utterances (may be noise)
    if (data.result && data.result.length < 5) {
      adjustedConfidence *= 0.8;
    }

    return Math.max(0.1, Math.min(1.0, adjustedConfidence));
  }

  private notifyListeners(state: SpeechRecognitionState): void {
    this.listeners.forEach(listener => {
      try {
        listener(state);
      } catch (error) {
        console.error('Error in speech recognition listener:', error);
      }
    });
  }

  public get isReady(): boolean {
    return this.isInitialized && this.whisperContext !== null;
  }

  public get isInitializingModel(): boolean {
    return this.isInitializing;
  }

  public async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initializeWhisper();
    }
  }

  public getModelInfo(): { modelName: string; isProduction: boolean; supportsArabic: boolean } {
    return {
      modelName: 'ggml-base-q8_0.bin',
      isProduction: !this.mockMode,
      supportsArabic: true,
    };
  }
}

// Singleton instance
export const speechRecognitionService = new SpeechRecognitionService();
