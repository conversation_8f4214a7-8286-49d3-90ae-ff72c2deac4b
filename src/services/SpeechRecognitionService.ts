import { initWhisper, WhisperContext } from 'whisper.rn';
import { Platform } from 'react-native';
import {
  SpeechRecognitionResult,
  SpeechRecognitionOptions,
  SpeechRecognitionState,
  ErrorType,
  AppError
} from '../types';

// Enhanced debugging interface for development
export interface DebugInfo {
  timestamp: number;
  type: 'info' | 'warning' | 'error' | 'transcription' | 'audio' | 'timing';
  message: string;
  data?: any;
}

export interface TranscriptionDebugData {
  text: string;
  confidence: number;
  processTime: number;
  recordingTime: number;
  isCapturing: boolean;
  sliceIndex?: number;
  segments?: Array<{
    text: string;
    start: number;
    end: number;
    confidence?: number;
  }>;
}

export class SpeechRecognitionService {
  private whisperContext: WhisperContext | null = null;
  private isInitialized = false;
  private isInitializing = false;
  private initializationPromise: Promise<void> | null = null;
  private currentSubscription: (() => void) | null = null;
  private listeners: Array<(state: SpeechR<PERSON>ognitionState) => void> = [];
  private debugListeners: Array<(debug: DebugInfo) => void> = [];
  private mockMode = false; // For development/testing

  // Enhanced debugging properties
  private debugMode = __DEV__; // Enable debugging in development
  private transcriptionHistory: TranscriptionDebugData[] = [];
  private sessionStartTime: number = 0;
  private totalProcessingTime: number = 0;
  private totalRecordingTime: number = 0;
  private sliceCount: number = 0;

  constructor() {
    // Don't initialize immediately to avoid blocking the main thread
    // Initialize lazily when first needed

    // Disable mock mode to enable real speech recognition
    this.mockMode = false;

    console.log('SpeechRecognitionService: Running in production mode with real-time transcription');

    if (this.debugMode) {
      this.logDebug('info', 'SpeechRecognitionService initialized with enhanced debugging enabled');
    }
  }

  // Enhanced debugging methods
  private logDebug(type: DebugInfo['type'], message: string, data?: any): void {
    if (!this.debugMode) return;

    const debugInfo: DebugInfo = {
      timestamp: Date.now(),
      type,
      message,
      data
    };

    // Console logging with enhanced formatting
    const timestamp = new Date(debugInfo.timestamp).toISOString();
    const prefix = `[${timestamp}] [WHISPER-${type.toUpperCase()}]`;

    switch (type) {
      case 'error':
        console.error(`${prefix} ${message}`, data || '');
        break;
      case 'warning':
        console.warn(`${prefix} ${message}`, data || '');
        break;
      case 'transcription':
        console.log(`${prefix} 🎤 ${message}`, data || '');
        break;
      case 'audio':
        console.log(`${prefix} 🔊 ${message}`, data || '');
        break;
      case 'timing':
        console.log(`${prefix} ⏱️ ${message}`, data || '');
        break;
      default:
        console.log(`${prefix} ${message}`, data || '');
    }

    // Notify debug listeners for UI display
    this.debugListeners.forEach(listener => {
      try {
        listener(debugInfo);
      } catch (error) {
        console.error('Error in debug listener:', error);
      }
    });
  }

  public addDebugListener(listener: (debug: DebugInfo) => void): () => void {
    this.debugListeners.push(listener);
    return () => {
      const index = this.debugListeners.indexOf(listener);
      if (index > -1) {
        this.debugListeners.splice(index, 1);
      }
    };
  }

  public getTranscriptionHistory(): TranscriptionDebugData[] {
    return [...this.transcriptionHistory];
  }

  public clearTranscriptionHistory(): void {
    this.transcriptionHistory = [];
    this.logDebug('info', 'Transcription history cleared');
  }

  public getSessionStats(): {
    sessionDuration: number;
    totalProcessingTime: number;
    totalRecordingTime: number;
    sliceCount: number;
    averageProcessingTime: number;
    averageConfidence: number;
  } {
    const sessionDuration = this.sessionStartTime > 0 ? Date.now() - this.sessionStartTime : 0;
    const averageProcessingTime = this.sliceCount > 0 ? this.totalProcessingTime / this.sliceCount : 0;
    const averageConfidence = this.transcriptionHistory.length > 0
      ? this.transcriptionHistory.reduce((sum, item) => sum + item.confidence, 0) / this.transcriptionHistory.length
      : 0;

    return {
      sessionDuration,
      totalProcessingTime: this.totalProcessingTime,
      totalRecordingTime: this.totalRecordingTime,
      sliceCount: this.sliceCount,
      averageProcessingTime,
      averageConfidence
    };
  }

  private async initializeWhisper(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    if (this.isInitializing && this.initializationPromise) {
      return this.initializationPromise;
    }

    this.isInitializing = true;
    this.initializationPromise = this._performInitialization();

    try {
      await this.initializationPromise;
    } catch (error) {
      this.isInitializing = false;
      this.initializationPromise = null;
      throw error;
    }
  }

  private async _performInitialization(): Promise<void> {
    try {
      this.logDebug('info', 'Starting Whisper model initialization for Arabic speech recognition');
      this.logDebug('info', `Platform: ${Platform.OS}`);
      this.logDebug('info', 'Model: ggml-base-q8_0.bin (production model)');

      // Notify listeners that initialization is starting
      this.notifyListeners({
        isListening: false,
        isProcessing: true, // Use processing state to indicate loading
      });

      // Mock mode for development
      if (this.mockMode) {
        console.log('Mock mode: Simulating model initialization...');
        await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate loading time

        this.isInitialized = true;
        this.isInitializing = false;

        console.log('Mock mode: Whisper model initialized successfully');

        this.notifyListeners({
          isListening: false,
          isProcessing: false,
        });
        return;
      }

      // Use the production-ready ggml-base-q8_0.bin model for better Arabic recognition
      let initOptions: any = {
        filePath: require('../../assets/model/ggml-base-q8_0.bin'),
      };

      // Add platform-specific options
      if (Platform.OS === 'ios') {
        initOptions.useGpu = true;
        this.logDebug('info', 'iOS detected: Enabling GPU acceleration');
      } else {
        // Android-specific optimizations
        initOptions.useGpu = false;
        initOptions.maxThreads = 2; // Limit threads on Android
        this.logDebug('info', 'Android detected: Using CPU with 2 threads');
      }

      this.logDebug('info', 'Initialization options configured', {
        platform: Platform.OS,
        useGpu: initOptions.useGpu,
        maxThreads: initOptions.maxThreads || 'default'
      });

      // Initialize Whisper with extended timeout for larger model
      // The ggml-base-q8_0.bin model is larger and may take longer to load
      const initStartTime = Date.now();
      this.logDebug('timing', 'Starting model loading - this may take up to 60 seconds...');

      const initPromise = initWhisper(initOptions);
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Initialization timeout after 60 seconds')), 60000);
      });

      this.whisperContext = await Promise.race([initPromise, timeoutPromise]) as any;

      const initTime = Date.now() - initStartTime;
      this.isInitialized = true;
      this.isInitializing = false;

      this.logDebug('timing', `Model initialized successfully in ${initTime}ms`, {
        modelSize: 'ggml-base-q8_0.bin',
        initTime,
        gpu: this.whisperContext?.gpu || false,
        contextId: this.whisperContext?.id
      });

      this.notifyListeners({
        isListening: false,
        isProcessing: false,
      });
    } catch (error) {
      this.isInitialized = false;
      this.isInitializing = false;

      console.error('Failed to initialize Whisper:', error);
      console.error('Error type:', typeof error);
      console.error('Error message:', (error as any)?.message);
      console.error('Error stack:', (error as any)?.stack);

      // Try to get more details about the error
      let errorMessage = 'Failed to initialize speech recognition model';
      const errorObj = error as any;
      if (errorObj?.message) {
        if (errorObj.message.includes('timeout')) {
          errorMessage = 'تم انتهاء وقت تحميل النموذج (60 ثانية). النموذج الكبير يحتاج وقت أطول. يرجى المحاولة مرة أخرى.';
        } else if (errorObj.message.includes('memory') || errorObj.message.includes('Memory')) {
          errorMessage = 'ذاكرة غير كافية لتحميل النموذج الكبير. يرجى إغلاق التطبيقات الأخرى والمحاولة مرة أخرى.';
        } else if (errorObj.message.includes('file') || errorObj.message.includes('File')) {
          errorMessage = 'لم يتم العثور على ملف النموذج ggml-base-q8_0.bin. يرجى إعادة تثبيت التطبيق.';
        }
      }

      this.notifyListeners({
        isListening: false,
        isProcessing: false,
        error: errorMessage,
      });

      throw new AppError({
        type: ErrorType.MODEL_LOADING_FAILED,
        message: errorMessage,
        details: error,
        timestamp: new Date(),
      });
    }
  }

  public async startListening(options: SpeechRecognitionOptions = {}): Promise<void> {
    // Ensure the service is initialized before starting
    await this.initializeWhisper();

    // Reset session tracking
    this.sessionStartTime = Date.now();
    this.totalProcessingTime = 0;
    this.totalRecordingTime = 0;
    this.sliceCount = 0;
    this.transcriptionHistory = [];

    this.logDebug('info', 'Starting real-time transcription session', {
      language: options.language || 'ar',
      useVad: options.useVad !== false,
      realtimeAudioSec: options.realtimeAudioSec || 30,
      realtimeAudioSliceSec: options.realtimeAudioSliceSec || 3
    });

    // Mock mode implementation
    if (this.mockMode) {
      this.logDebug('warning', 'Running in mock mode - no actual speech recognition');
      this.notifyListeners({
        isListening: true,
        isProcessing: false,
      });

      // Simulate speech recognition after a delay
      setTimeout(() => {
        this.notifyListeners({
          isListening: false,
          isProcessing: true,
          currentResult: {
            text: 'مرحبا بك في التطبيق', // Mock Arabic text
            confidence: 0.95,
          },
        });
      }, 3000);

      return;
    }

    if (!this.isInitialized || !this.whisperContext) {
      throw new AppError({
        type: ErrorType.SPEECH_RECOGNITION_FAILED,
        message: 'Speech recognition service not initialized',
        timestamp: new Date(),
      });
    }

    try {
      this.notifyListeners({
        isListening: true,
        isProcessing: false,
      });

      const transcribeOptions = {
        language: options.language || 'ar', // Default to Arabic
        maxThreads: options.maxThreads || (Platform.OS === 'android' ? 2 : 4),
        useVad: options.useVad !== false, // Enable VAD by default
        realtimeAudioSec: options.realtimeAudioSec || 30,
        realtimeAudioSliceSec: options.realtimeAudioSliceSec || 3, // Shorter slices for better responsiveness
        realtimeAudioMinSec: 1, // Minimum audio duration before processing
        // Enhanced Arabic-specific optimizations for ggml-base-q8_0 model
        temperature: 0.0, // More deterministic results
        beamSize: 5, // Good balance between accuracy and speed
        bestOf: 5, // Multiple candidates for better accuracy
        wordThold: 0.01, // Lower threshold for Arabic words
        tokenTimestamps: true,
        // Additional optimizations for Arabic
        noSpeechThold: 0.6, // Adjust for Arabic speech patterns
        logprobThold: -1.0, // Better handling of Arabic phonemes
        compressionRatioThold: 2.4, // Optimized for Arabic text compression
        // VAD-specific settings for better Arabic speech detection
        vadThold: 0.5, // Voice activity detection threshold
        vadMs: 2000, // VAD window size in milliseconds
        vadFreqThold: 100, // Frequency threshold for VAD
        // Audio session configuration for iOS
        ...(Platform.OS === 'ios' && {
          audioSessionOnStartIos: {
            category: 'PlayAndRecord' as any,
            options: ['MixWithOthers' as any],
            mode: 'Default' as any,
          },
          audioSessionOnStopIos: 'restore' as any,
        }),
      };

      this.logDebug('audio', 'Starting real-time transcription with optimized Arabic settings', transcribeOptions);

      const { stop, subscribe } = await this.whisperContext.transcribeRealtime(transcribeOptions);

      this.currentSubscription = stop;

      subscribe((event) => {
        const { isCapturing, data, processTime, recordingTime, error, slices, code, jobId, contextId } = event;

        // Log detailed event information
        this.logDebug('audio', `Transcription event received`, {
          isCapturing,
          hasData: !!data,
          processTime,
          recordingTime,
          error,
          sliceCount: slices?.length || 0,
          code,
          jobId,
          contextId
        });

        // Process slices for intermediate results
        if (slices && slices.length > 0) {
          slices.forEach((slice, index) => {
            if (slice.data?.result) {
              this.logDebug('transcription',
                `Slice ${index + 1}/${slices.length}: "${slice.data.result}"`,
                {
                  sliceProcessTime: slice.processTime,
                  sliceRecordingTime: slice.recordingTime,
                  sliceError: slice.error
                }
              );
            }
          });
        }

        if (error) {
          this.logDebug('error', `Real-time transcription error: ${error}`, { event });
          this.notifyListeners({
            isListening: false,
            isProcessing: false,
            error: `خطأ في التعرف على الصوت: ${error}`,
          });
          return;
        }

        // Update session statistics
        if (processTime > 0) {
          this.totalProcessingTime += processTime;
          this.sliceCount++;
        }
        if (recordingTime > 0) {
          this.totalRecordingTime = Math.max(this.totalRecordingTime, recordingTime);
        }

        // Enhanced state management for real-time transcription
        const hasResult = data?.result && data.result.trim().length > 0;
        const confidence = hasResult ? this.calculateConfidence(data) : 0;

        // Log transcription results
        if (hasResult) {
          const debugData: TranscriptionDebugData = {
            text: data.result.trim(),
            confidence,
            processTime,
            recordingTime,
            isCapturing,
            sliceIndex: this.sliceCount,
            segments: data.segments?.map(segment => ({
              text: segment.text.trim(),
              start: segment.t0,
              end: segment.t1,
              confidence: (segment as any).p || 0 // Confidence may not be available in all versions
            })).filter(segment => segment.text.length > 0)
          };

          this.transcriptionHistory.push(debugData);

          this.logDebug('transcription',
            `${isCapturing ? 'Intermediate' : 'Final'} result: "${data.result.trim()}"`,
            {
              confidence: confidence.toFixed(3),
              processTime: `${processTime}ms`,
              recordingTime: `${recordingTime}ms`,
              sliceIndex: this.sliceCount,
              segmentCount: debugData.segments?.length || 0
            }
          );
        }

        // Log timing information
        if (processTime > 0 || recordingTime > 0) {
          this.logDebug('timing',
            `Processing: ${processTime}ms, Recording: ${recordingTime}ms`,
            {
              averageProcessingTime: this.sliceCount > 0 ? (this.totalProcessingTime / this.sliceCount).toFixed(1) : 0,
              totalSlices: this.sliceCount,
              sessionDuration: Date.now() - this.sessionStartTime
            }
          );
        }

        this.notifyListeners({
          isListening: isCapturing,
          isProcessing: !isCapturing && Boolean(data?.result),
          currentResult: hasResult ? {
            text: data.result.trim(),
            confidence,
            segments: data.segments?.map(segment => ({
              text: segment.text.trim(),
              start: segment.t0,
              end: segment.t1,
            })).filter(segment => segment.text.length > 0),
          } : undefined,
        });

        if (!isCapturing && hasResult) {
          // Final result received - log comprehensive session stats
          const sessionStats = this.getSessionStats();
          this.logDebug('info', 'Arabic speech recognition session completed', {
            finalText: data.result.trim(),
            finalConfidence: confidence.toFixed(3),
            sessionStats
          });
        }
      });

    } catch (error) {
      console.error('Failed to start speech recognition:', error);
      this.notifyListeners({
        isListening: false,
        isProcessing: false,
        error: 'Failed to start speech recognition',
      });
      throw new AppError({
        type: ErrorType.SPEECH_RECOGNITION_FAILED,
        message: 'Failed to start speech recognition',
        details: error,
        timestamp: new Date(),
      });
    }
  }

  public async stopListening(): Promise<void> {
    const sessionDuration = this.sessionStartTime > 0 ? Date.now() - this.sessionStartTime : 0;

    this.logDebug('info', 'Stopping real-time transcription session', {
      sessionDuration: `${sessionDuration}ms`,
      totalSlices: this.sliceCount,
      transcriptionCount: this.transcriptionHistory.length
    });

    // Mock mode implementation
    if (this.mockMode) {
      this.logDebug('warning', 'Stopping mock mode session');
      this.notifyListeners({
        isListening: false,
        isProcessing: false,
      });
      return;
    }

    if (this.currentSubscription) {
      try {
        this.currentSubscription();
        this.currentSubscription = null;

        // Log final session statistics
        const finalStats = this.getSessionStats();
        this.logDebug('info', 'Real-time transcription session ended', finalStats);

        this.notifyListeners({
          isListening: false,
          isProcessing: false,
        });
      } catch (error) {
        this.logDebug('error', 'Failed to stop speech recognition', error);
        this.notifyListeners({
          isListening: false,
          isProcessing: false,
          error: 'Failed to stop speech recognition',
        });
      }
    } else {
      this.logDebug('warning', 'No active subscription to stop');
    }
  }

  public async transcribeAudio(audioPath: string, options: SpeechRecognitionOptions = {}): Promise<SpeechRecognitionResult> {
    // Ensure the service is initialized before transcribing
    await this.initializeWhisper();

    if (!this.isInitialized || !this.whisperContext) {
      throw new AppError({
        type: ErrorType.SPEECH_RECOGNITION_FAILED,
        message: 'Speech recognition service not initialized',
        timestamp: new Date(),
      });
    }

    try {
      const transcribeOptions = {
        language: options.language || 'ar',
        maxThreads: options.maxThreads || (Platform.OS === 'android' ? 2 : 4),
        // Enhanced Arabic-specific optimizations for ggml-base-q8_0 model
        temperature: 0.0,
        beamSize: 5,
        bestOf: 5,
        wordThold: 0.01,
        tokenTimestamps: true,
        // Additional optimizations for Arabic
        noSpeechThold: 0.6,
        logprobThold: -1.0,
        compressionRatioThold: 2.4,
      };

      const { promise } = this.whisperContext.transcribe(audioPath, transcribeOptions);
      const result = await promise;

      return {
        text: result.result,
        confidence: this.calculateConfidence(result),
        segments: result.segments?.map(segment => ({
          text: segment.text,
          start: segment.t0,
          end: segment.t1,
        })),
      };
    } catch (error) {
      console.error('Failed to transcribe audio:', error);
      throw new AppError({
        type: ErrorType.SPEECH_RECOGNITION_FAILED,
        message: 'Failed to transcribe audio',
        details: error,
        timestamp: new Date(),
      });
    }
  }

  public addListener(listener: (state: SpeechRecognitionState) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  public async release(): Promise<void> {
    try {
      await this.stopListening();
      if (this.whisperContext) {
        await this.whisperContext.release();
        this.whisperContext = null;
      }
      this.isInitialized = false;
      this.listeners = [];
    } catch (error) {
      console.error('Failed to release speech recognition service:', error);
    }
  }

  private calculateConfidence(data: any): number {
    // Enhanced confidence calculation for Arabic speech recognition
    if (!data || !data.segments || data.segments.length === 0) {
      return 0.5; // Default confidence
    }

    // Calculate average confidence from segments
    const avgConfidence = data.segments.reduce((sum: number, segment: any) => {
      return sum + (segment.p || 0.5);
    }, 0) / data.segments.length;

    // Apply Arabic-specific confidence adjustments
    let adjustedConfidence = avgConfidence;

    // Boost confidence for longer Arabic text (more context)
    if (data.result && data.result.length > 20) {
      adjustedConfidence *= 1.1;
    }

    // Reduce confidence for very short utterances (may be noise)
    if (data.result && data.result.length < 5) {
      adjustedConfidence *= 0.8;
    }

    return Math.max(0.1, Math.min(1.0, adjustedConfidence));
  }

  private notifyListeners(state: SpeechRecognitionState): void {
    this.listeners.forEach(listener => {
      try {
        listener(state);
      } catch (error) {
        console.error('Error in speech recognition listener:', error);
      }
    });
  }

  public get isReady(): boolean {
    return this.isInitialized && this.whisperContext !== null;
  }

  public get isInitializingModel(): boolean {
    return this.isInitializing;
  }

  public async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initializeWhisper();
    }
  }

  public getModelInfo(): { modelName: string; isProduction: boolean; supportsArabic: boolean } {
    return {
      modelName: 'ggml-base-q8_0.bin',
      isProduction: !this.mockMode,
      supportsArabic: true,
    };
  }
}

// Singleton instance
export const speechRecognitionService = new SpeechRecognitionService();
