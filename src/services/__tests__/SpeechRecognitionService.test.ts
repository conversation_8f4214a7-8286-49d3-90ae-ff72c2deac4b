import { SpeechRecognitionService } from '../SpeechRecognitionService';

// Mock whisper.rn
const mockTranscribeResult = {
  result: 'مرحبا بك في التطبيق',
  segments: [
    { text: 'مرحبا', t0: 0, t1: 1000, p: 0.95 },
    { text: 'بك', t0: 1000, t1: 1500, p: 0.90 },
    { text: 'في', t0: 1500, t1: 2000, p: 0.92 },
    { text: 'التطبيق', t0: 2000, t1: 3000, p: 0.88 },
  ],
};

jest.mock('whisper.rn', () => ({
  initWhisper: jest.fn().mockResolvedValue({
    transcribeRealtime: jest.fn().mockResolvedValue({
      stop: jest.fn(),
      subscribe: jest.fn(),
    }),
    transcribe: jest.fn().mockReturnValue({
      promise: Promise.resolve(mockTranscribeResult),
    }),
    release: jest.fn(),
  }),
}));

describe('SpeechRecognitionService', () => {
  let service: SpeechRecognitionService;

  beforeEach(() => {
    service = new SpeechRecognitionService();
  });

  afterEach(async () => {
    await service.release();
  });

  describe('Initialization', () => {
    it('should initialize with production mode disabled mock', () => {
      expect(service.isReady).toBe(false);
      expect(service.isInitializingModel).toBe(false);
    });

    it('should provide correct model information', () => {
      const modelInfo = service.getModelInfo();
      expect(modelInfo.modelName).toBe('ggml-base-q8_0.bin');
      expect(modelInfo.isProduction).toBe(true);
      expect(modelInfo.supportsArabic).toBe(true);
    });

    it('should initialize successfully', async () => {
      await service.ensureInitialized();
      expect(service.isReady).toBe(true);
    });
  });

  describe('Real-time Transcription', () => {
    beforeEach(async () => {
      await service.ensureInitialized();
    });

    it('should start listening with Arabic-specific options', async () => {
      const listener = jest.fn();
      service.addListener(listener);

      await service.startListening({
        language: 'ar',
        useVad: true,
        realtimeAudioSec: 30,
        realtimeAudioSliceSec: 3,
      });

      // Verify that the listener was called with listening state
      expect(listener).toHaveBeenCalledWith(
        expect.objectContaining({
          isListening: true,
          isProcessing: false,
        })
      );
    });

    it('should handle Arabic text transcription correctly', async () => {
      const result = await service.transcribeAudio('/path/to/audio.wav', {
        language: 'ar',
      });

      expect(result.text).toBe('مرحبا بك في التطبيق');
      expect(result.confidence).toBeGreaterThan(0);
      expect(result.segments).toHaveLength(4);
      expect(result.segments![0].text).toBe('مرحبا');
    });

    it('should calculate confidence correctly for Arabic text', async () => {
      const result = await service.transcribeAudio('/path/to/audio.wav');

      // Should have reasonable confidence for good Arabic transcription
      expect(result.confidence).toBeGreaterThan(0.8);
      expect(result.confidence).toBeLessThanOrEqual(1.0);
    });
  });

  describe('Error Handling', () => {
    it('should handle initialization timeout gracefully', async () => {
      // Mock a timeout scenario
      const mockInitWhisper = require('whisper.rn').initWhisper;
      mockInitWhisper.mockImplementationOnce(() =>
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Initialization timeout')), 100)
        )
      );

      const newService = new SpeechRecognitionService();

      await expect(newService.ensureInitialized()).rejects.toThrow();
    });

    it('should provide Arabic error messages', async () => {
      const listener = jest.fn();
      service.addListener(listener);

      // Mock an error in transcription
      const mockTranscribeRealtime = jest.fn().mockResolvedValue({
        stop: jest.fn(),
        subscribe: jest.fn((callback) => {
          // Simulate an error callback
          setTimeout(() => {
            callback({
              isCapturing: false,
              data: null,
              error: 'Audio input error',
            });
          }, 10);
        }),
      });

      await service.ensureInitialized();

      // Override the mock for this specific test
      const whisperMock = require('whisper.rn');
      whisperMock.initWhisper.mockResolvedValueOnce({
        transcribeRealtime: mockTranscribeRealtime,
        release: jest.fn(),
      });

      // Re-initialize with the error mock
      const errorService = new SpeechRecognitionService();
      errorService.addListener(listener);
      await errorService.ensureInitialized();
      await errorService.startListening();

      // Wait for the error callback
      await new Promise(resolve => setTimeout(resolve, 50));

      // Should receive Arabic error message
      expect(listener).toHaveBeenCalledWith(
        expect.objectContaining({
          error: expect.stringContaining('خطأ في التعرف على الصوت'),
        })
      );
    });
  });

  describe('State Management', () => {
    it('should manage listeners correctly', () => {
      const listener1 = jest.fn();
      const listener2 = jest.fn();

      const unsubscribe1 = service.addListener(listener1);
      const unsubscribe2 = service.addListener(listener2);

      // Both listeners should be called
      service['notifyListeners']({ isListening: false, isProcessing: false });
      expect(listener1).toHaveBeenCalled();
      expect(listener2).toHaveBeenCalled();

      // Remove first listener
      unsubscribe1();
      listener1.mockClear();
      listener2.mockClear();

      service['notifyListeners']({ isListening: true, isProcessing: false });
      expect(listener1).not.toHaveBeenCalled();
      expect(listener2).toHaveBeenCalled();
    });

    it('should stop listening correctly', async () => {
      await service.ensureInitialized();
      await service.startListening();
      await service.stopListening();

      expect(service.isReady).toBe(true);
    });
  });
});
