/**
 * Arabic Text Memorization App
 * React Native app for Arabic text memorization with speech recognition
 */

import React, { useState, useEffect } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Alert,
  I18nManager,
  Platform,
} from 'react-native';
import { request, PERMISSIONS, RESULTS } from 'react-native-permissions';
import { ArabicText, TextSegment, PracticeSession, AppState } from './src/types';
import { SAMPLE_ARABIC_TEXTS, createTextSegments } from './src/utils/arabicTextUtils';
import ArabicTextDisplay from './src/components/ArabicTextDisplay';
import PracticeMode from './src/components/PracticeMode';
import ErrorBoundary from './src/components/ErrorBoundary';

// Enable RTL layout
I18nManager.allowRTL(true);
I18nManager.forceRTL(true);

type AppMode = 'selection' | 'practice';

function App(): React.JSX.Element {
  const [appState, setAppState] = useState<AppState>({
    isLoading: false,
    audioPermissions: {
      granted: false,
      canAskAgain: true,
    },
  });

  const [currentMode, setCurrentMode] = useState<AppMode>('selection');
  const [selectedText, setSelectedText] = useState<ArabicText>(SAMPLE_ARABIC_TEXTS[0]);
  const [textSegments, setTextSegments] = useState<TextSegment[]>([]);
  const [practiceSession, setPracticeSession] = useState<PracticeSession | null>(null);

  // Initialize text segments when text changes
  useEffect(() => {
    const segments = createTextSegments(selectedText.content, 'word');
    setTextSegments(segments);
  }, [selectedText]);

  // Request microphone permissions on app start
  useEffect(() => {
    requestMicrophonePermission();
  }, []);

  const requestMicrophonePermission = async () => {
    try {
      const permission = Platform.OS === 'ios'
        ? PERMISSIONS.IOS.MICROPHONE
        : PERMISSIONS.ANDROID.RECORD_AUDIO;

      const result = await request(permission);

      setAppState(prev => ({
        ...prev,
        audioPermissions: {
          granted: result === RESULTS.GRANTED,
          canAskAgain: result !== RESULTS.BLOCKED,
        },
      }));

      if (result === RESULTS.BLOCKED) {
        Alert.alert(
          'إذن الميكروفون مطلوب',
          'يحتاج التطبيق إلى إذن الميكروفون للتعرف على الصوت. يرجى تفعيله من إعدادات التطبيق.',
          [{ text: 'موافق' }]
        );
      }
    } catch (error) {
      console.error('Error requesting microphone permission:', error);
    }
  };

  const handleSegmentSelect = (segment: TextSegment) => {
    setTextSegments(prev =>
      prev.map(s =>
        s.id === segment.id
          ? { ...s, isSelected: segment.isSelected }
          : s
      )
    );
  };

  const handleSelectionChange = (start: number, end: number) => {
    // Handle text selection changes if needed
    console.log('Selection changed:', start, end);
  };

  const handleStartPractice = () => {
    const selectedSegments = textSegments.filter(s => s.isSelected);

    if (selectedSegments.length === 0) {
      Alert.alert('تنبيه', 'يرجى تحديد النص المراد التدرب عليه أولاً.');
      return;
    }

    if (!appState.audioPermissions.granted) {
      Alert.alert(
        'إذن الميكروفون مطلوب',
        'يحتاج التطبيق إلى إذن الميكروفون للتعرف على الصوت.',
        [
          { text: 'إلغاء', style: 'cancel' },
          { text: 'طلب الإذن', onPress: requestMicrophonePermission },
        ]
      );
      return;
    }

    const session: PracticeSession = {
      id: `session-${Date.now()}`,
      textId: selectedText.id,
      selectedSegments,
      startTime: new Date(),
      progress: 0,
      isCompleted: false,
    };

    setPracticeSession(session);
    setCurrentMode('practice');
  };

  const handleWordRevealed = (wordIndex: number) => {
    // Update the corresponding segment as revealed
    setTextSegments(prev => {
      const newSegments = [...prev];
      let currentWordIndex = 0;

      for (let i = 0; i < newSegments.length; i++) {
        if (newSegments[i].isSelected) {
          if (currentWordIndex === wordIndex) {
            newSegments[i] = { ...newSegments[i], isRevealed: true };
            break;
          }
          currentWordIndex++;
        }
      }

      return newSegments;
    });
  };

  const handleSessionComplete = () => {
    Alert.alert(
      'تهانينا!',
      'لقد أكملت التدريب بنجاح!',
      [
        {
          text: 'العودة للنص',
          onPress: () => {
            setCurrentMode('selection');
            setPracticeSession(null);
          }
        },
        {
          text: 'تدريب جديد',
          onPress: () => {
            // Reset segments and start new practice
            setTextSegments(prev =>
              prev.map(s => ({ ...s, isSelected: false, isRevealed: false }))
            );
            setCurrentMode('selection');
            setPracticeSession(null);
          }
        }
      ]
    );
  };

  const handleSessionPause = () => {
    setCurrentMode('selection');
  };

  const handleSessionReset = () => {
    setTextSegments(prev =>
      prev.map(s => ({ ...s, isRevealed: false }))
    );
    setPracticeSession(null);
    setCurrentMode('selection');
  };

  const handleTextChange = (text: ArabicText) => {
    setSelectedText(text);
    setCurrentMode('selection');
    setPracticeSession(null);
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <Text style={styles.headerTitle}>مساعد حفظ النصوص العربية</Text>
      <Text style={styles.headerSubtitle}>التدريب بالذكاء الاصطناعي</Text>
    </View>
  );

  const renderTextSelector = () => (
    <View style={styles.textSelector}>
      <Text style={styles.selectorTitle}>اختر النص:</Text>
      <View style={styles.textButtons}>
        {SAMPLE_ARABIC_TEXTS.map((text) => (
          <TouchableOpacity
            key={text.id}
            style={[
              styles.textButton,
              selectedText.id === text.id && styles.selectedTextButton
            ]}
            onPress={() => handleTextChange(text)}
          >
            <Text style={[
              styles.textButtonText,
              selectedText.id === text.id && styles.selectedTextButtonText
            ]}>
              {text.title}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderModeSelector = () => {
    if (currentMode === 'practice') return null;

    const selectedCount = textSegments.filter(s => s.isSelected).length;

    return (
      <View style={styles.modeSelector}>
        <Text style={styles.selectionInfo}>
          تم تحديد {selectedCount} كلمة
        </Text>
        <TouchableOpacity
          style={[
            styles.practiceButton,
            selectedCount === 0 && styles.disabledButton
          ]}
          onPress={handleStartPractice}
          disabled={selectedCount === 0}
        >
          <Text style={styles.practiceButtonText}>
            بدء التدريب
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />

      {renderHeader()}

      {currentMode === 'selection' && (
        <>
          {renderTextSelector()}
          <ArabicTextDisplay
            text={selectedText}
            segments={textSegments}
            onSegmentSelect={handleSegmentSelect}
            onSelectionChange={handleSelectionChange}
          />
          {renderModeSelector()}
        </>
      )}

      {currentMode === 'practice' && practiceSession && (
        <ErrorBoundary
          onError={(error, errorInfo) => {
            console.error('PracticeMode error:', error, errorInfo);
            // Optionally, you could automatically return to selection mode
            // setCurrentMode('selection');
          }}
        >
          <PracticeMode
            session={practiceSession}
            onWordRevealed={handleWordRevealed}
            onSessionComplete={handleSessionComplete}
            onSessionPause={handleSessionPause}
            onSessionReset={handleSessionReset}
          />
        </ErrorBoundary>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    backgroundColor: '#3498DB',
    padding: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#ECF0F1',
    textAlign: 'center',
    marginTop: 5,
  },
  textSelector: {
    padding: 15,
    backgroundColor: '#F8F9FA',
    borderBottomWidth: 1,
    borderBottomColor: '#E9ECEF',
  },
  selectorTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 10,
    textAlign: 'center',
  },
  textButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  textButton: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    margin: 5,
    borderWidth: 1,
    borderColor: '#BDC3C7',
  },
  selectedTextButton: {
    backgroundColor: '#3498DB',
    borderColor: '#3498DB',
  },
  textButtonText: {
    fontSize: 14,
    color: '#2C3E50',
  },
  selectedTextButtonText: {
    color: '#FFFFFF',
  },
  modeSelector: {
    padding: 20,
    backgroundColor: '#F8F9FA',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#E9ECEF',
  },
  selectionInfo: {
    fontSize: 16,
    color: '#7F8C8D',
    marginBottom: 15,
    textAlign: 'center',
  },
  practiceButton: {
    backgroundColor: '#27AE60',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    minWidth: 150,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#BDC3C7',
  },
  practiceButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default App;
